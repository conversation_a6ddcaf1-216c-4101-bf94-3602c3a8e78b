import { useInfiniteModels } from "@/api";
import ChatPopup from "@/components/chatbot/ChatPopup";
import SearchBar from "@/components/common/Search";
import InfiniteMembersList from "@/components/memberslist/InfiniteMembersList";
import { useAutoScroll, useFormattedFilters } from "@/hooks";
import { useTranslation } from "@/hooks/useTranslation";
import { useMemo, useState } from "react";
import { Container } from "react-bootstrap";
import "./styles.scss";

interface HomeProps {
  isFavorite?: boolean;
}

const Home = ({ isFavorite }: HomeProps) => {
  const { t } = useTranslation();
  const initialFilter = {
    name: undefined,
    relationshipStatusId: undefined,
    hairColorId: undefined,
    bestFeatureId: undefined,
    eyeColorId: undefined,
    personalityId: undefined,
    appearanceId: undefined,
    bodyTypeId: undefined,
    smokingHabitId: undefined,
    drinkingHabitId: undefined,
    interestIds: undefined,
    ageRange: undefined,
    distance: undefined,
    withPhotos: false,
    isFavorite: undefined,
  };

  const [filters, setFilters] = useState(initialFilter);
  const limit = 50;

  const formattedFilters = useFormattedFilters(filters);
  const [params, setParams] = useState(formattedFilters);

  const {
    data,
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
    isError,
    refetch: refetchModels,
  } = useInfiniteModels({
    params: { ...params, limit, isFavorite },
  });

  const models = useMemo(() => {
    return data?.pages?.flatMap((page) => page?.data?.models || []) || [];
  }, [data]);

  useAutoScroll();

  const onClearFilters = () => {
    setFilters(initialFilter);
    setParams(initialFilter);
  };

  const onSearchWithFilters = () => {
    setParams(formattedFilters);
  };

  return (
    <>
      <div className="banner-img">
        <Container fluid>
          <div className="d-flex flex-column gap-2 mb-3">
            <h2 className="title mb-0">{t("home.title")}</h2>
            <p className="description mb-0">{t("home.description")}</p>
          </div>
          <SearchBar
            filters={filters}
            setFilters={setFilters}
            onClearFilters={onClearFilters}
            onSearchWithFilters={onSearchWithFilters}
            setParams={setParams}
          />
        </Container>
      </div>
      <div className="members-list">
        <Container fluid>
          {isError || models.length === 0 ? (
            <div className="text-center py-5">
              <h5 className="fw-bold mb-2">No matches found</h5>
              <p className="text-muted mb-0">
                We couldn't find any models matching your preferences right now.
                <br />
                Try adjusting your filters or check back later for new faces!
              </p>
            </div>
          ) : (
            <InfiniteMembersList
              models={models}
              hasNextPage={hasNextPage}
              isLoading={isLoading}
              isFetchingNextPage={isFetchingNextPage}
              fetchNextPage={fetchNextPage}
              refetchModels={refetchModels}
            />
          )}
        </Container>
      </div>
      <ChatPopup />
    </>
  );
};

export default Home;
