import { IDType } from "@/types";

export const API_ENDPOINTS = {
  SIGNUP: "/auth/sign-up",
  RESET_PASSWORD: "/auth/reset-password",
  CHANGE_PASSWORD: "/my-account/change-password",
  LOGIN: "/auth/login",
  COUNTRIES: "/master/countries",
  MASTER: "/master?lang=en",
  MY_PROFILE: "/my-account/profile",
  UPDATE_PROFILE: "/my-account/profile",
  VERIFY_OTP: "/auth/verify-otp",
  REQUEST_OTP: "/auth/request-otp",
  LOGOUT: "/auth/logout",
  GET_S3_PRE_SIGNED_URL: "/utils/s3-upload-urls",
  UPDATE_AVATAR: "/my-account/avatar",
  UPDATE_NOTIFICATIONS: "/my-account/notification",
  UPLOAD_ALBUM: "/my-account/album-upload",
  DELETE_IMAGE: (id: any) => `/my-account/image/${id}`,

  // flirts
  SEND_FLIRT: "/flirt/send",
  GET_SENT_FLIRTS: "/flirt/sent",
  GET_RECEIVED_FLIRTS: "/flirt/received",
  DELETE_FLIRT: (id: any) => `/flirt/${id}`,

  // models
  GET_MODELS: "/models",
  GET_FLIRT_MESSAGES: "/models/flirt-messages",
  GET_BOT_MESSAGES: "/models/bot-messages",
  GET_MODEL_DETAILS: (id: IDType) => `/models/${id}`,
  TOGGLE_FAVORITE: "/models/favorite",

  // packages
  GET_PACKAGES: "/packages",
  PURCHASE_PACKAGE: "/packages/purchase",
  GET_ACTIVE_PACKAGE: "/packages/active-package",
  // static pages
  PAGE: (slug: string) => `/page/${slug}`,
};
