import { API_ENDPOINTS } from "@/globals";
import { useMutation, useQuery } from "@tanstack/react-query";
import { apiClient } from "./apiClient";

export const useGetSentOrReceivedFlirts = ({ type = "sent" }) =>
  useQuery({
    queryFn: async () => {
      const url =
        type === "sent"
          ? API_ENDPOINTS.GET_SENT_FLIRTS
          : API_ENDPOINTS.GET_RECEIVED_FLIRTS;

      const response = await apiClient.get(url);
      return response;
    },
    queryKey: [`${type}-flirts`],
  });

export const useSendFlirtMutation = () => {
  return useMutation({
    mutationFn: (payload: Record<string, any>) => {
      return apiClient.post(API_ENDPOINTS.SEND_FLIRT, payload);
    },
  });
};
